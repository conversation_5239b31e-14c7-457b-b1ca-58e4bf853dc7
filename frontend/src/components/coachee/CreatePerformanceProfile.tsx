import { useState } from "react";
import { But<PERSON> } from "../ui/Button";
import { Card, CardHeader } from "../ui/Card";
import { LikertResponse } from "../ui/form/LikertResponse";
import {
  PerformanceCategory,
  GoalFormData,
} from "../../types/api/performanceProfile.types";
import { createPerformanceProfile } from "../../api/performanceProfile";
import { PageHeader } from "../ui/PageHeader";
import { Plus, Trash2, Target, Zap, Brain, Users } from "lucide-react";

interface CreatePerformanceProfileProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const CreatePerformanceProfile = ({
  onSuccess,
  onCancel,
}: CreatePerformanceProfileProps) => {
  const [startDate, setStartDate] = useState("");
  const [targetDate, setTargetDate] = useState("");
  const [goals, setGoals] = useState<
    Record<PerformanceCategory, GoalFormData[]>
  >({
    [PerformanceCategory.PHYSICAL]: [],
    [PerformanceCategory.TECHNICAL]: [],
    [PerformanceCategory.TACTICAL]: [],
    [PerformanceCategory.MENTAL]: [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const categoryConfig = {
    [PerformanceCategory.PHYSICAL]: {
      label: "Physical",
      icon: Zap,
      color: "text-red-600",
      bgColor: "bg-red-50",
      borderColor: "border-red-200",
    },
    [PerformanceCategory.TECHNICAL]: {
      label: "Technical",
      icon: Target,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
    },
    [PerformanceCategory.TACTICAL]: {
      label: "Tactical",
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
    },
    [PerformanceCategory.MENTAL]: {
      label: "Mental",
      icon: Brain,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
    },
  };

  const addGoal = (category: PerformanceCategory) => {
    if (goals[category].length >= 12) {
      setError(`Maximum 12 goals allowed per category`);
      return;
    }

    setGoals((prev) => ({
      ...prev,
      [category]: [
        ...prev[category],
        {
          category,
          goalName: "",
          currentRating: 5,
          targetRating: 8,
        },
      ],
    }));
    setError(null);
  };

  const removeGoal = (category: PerformanceCategory, index: number) => {
    setGoals((prev) => ({
      ...prev,
      [category]: prev[category].filter((_, i) => i !== index),
    }));
  };

  const updateGoal = (
    category: PerformanceCategory,
    index: number,
    field: keyof GoalFormData,
    value: any
  ) => {
    setGoals((prev) => ({
      ...prev,
      [category]: prev[category].map((goal, i) =>
        i === index ? { ...goal, [field]: value } : goal
      ),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validation
    if (!startDate || !targetDate) {
      setError("Start date and target date are required");
      return;
    }

    const start = new Date(startDate);
    const target = new Date(targetDate);

    if (target <= start) {
      setError("Target date must be after start date");
      return;
    }

    // Collect all goals
    const allGoals = Object.values(goals).flat();

    if (allGoals.length === 0) {
      setError("At least one goal is required");
      return;
    }

    // Validate goals
    for (const goal of allGoals) {
      if (!goal.goalName.trim()) {
        setError("All goals must have a name");
        return;
      }
    }

    setIsSubmitting(true);

    try {
      await createPerformanceProfile({
        startDate,
        targetDate,
        goals: allGoals,
      });

      onSuccess();
    } catch (error: any) {
      console.error("Error creating performance profile:", error);
      setError(
        error.response?.data?.error || "Failed to create performance profile"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Create Performance Profile"
        description="Set your performance goals across different categories"
      />

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Date Inputs */}
        <Card>
          <CardHeader title="Timeline" />
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start Date
                </label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Date
                </label>
                <input
                  type="date"
                  value={targetDate}
                  onChange={(e) => setTargetDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Goals by Category */}
        {Object.entries(categoryConfig).map(([category, config]) => {
          const categoryGoals = goals[category as PerformanceCategory];
          const Icon = config.icon;

          return (
            <Card key={category}>
              <CardHeader title={`${config.label} Goals`} />
              <div className="flex items-center justify-between px-6 pt-6">
                <div className="flex items-center space-x-2">
                  <Icon className={`h-5 w-5 ${config.color}`} />
                  <h3 className="text-lg font-medium text-gray-900">
                    {config.label} Goals
                  </h3>
                  <span className="text-sm text-gray-500">
                    ({categoryGoals.length}/12)
                  </span>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addGoal(category as PerformanceCategory)}
                  disabled={categoryGoals.length >= 12}
                  className="flex items-center space-x-1"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Goal</span>
                </Button>
              </div>
              <div className="p-6 space-y-4">
                {categoryGoals.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">
                    No goals added yet. Click "Add Goal" to get started.
                  </p>
                ) : (
                  categoryGoals.map((goal, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border ${config.bgColor} ${config.borderColor}`}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Goal Name
                          </label>
                          <input
                            type="text"
                            value={goal.goalName}
                            onChange={(e) =>
                              updateGoal(
                                category as PerformanceCategory,
                                index,
                                "goalName",
                                e.target.value
                              )
                            }
                            placeholder="Enter goal name..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                          />
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            removeGoal(category as PerformanceCategory, index)
                          }
                          className="ml-4 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Current Rating (0-10)
                          </label>
                          <LikertResponse
                            value={goal.currentRating}
                            scaleMin={0}
                            scaleMax={10}
                            labels={{
                              0: "Poor",
                              5: "Average",
                              10: "Excellent",
                            }}
                            onChange={(value) =>
                              updateGoal(
                                category as PerformanceCategory,
                                index,
                                "currentRating",
                                value as number
                              )
                            }
                            isCompleted={false}
                            saving={false}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Target Rating (0-10)
                          </label>
                          <LikertResponse
                            value={goal.targetRating}
                            scaleMin={0}
                            scaleMax={10}
                            labels={{
                              0: "Poor",
                              5: "Average",
                              10: "Excellent",
                            }}
                            onChange={(value) =>
                              updateGoal(
                                category as PerformanceCategory,
                                index,
                                "targetRating",
                                value as number
                              )
                            }
                            isCompleted={false}
                            saving={false}
                          />
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </Card>
          );
        })}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Performance Profile"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CreatePerformanceProfile;
